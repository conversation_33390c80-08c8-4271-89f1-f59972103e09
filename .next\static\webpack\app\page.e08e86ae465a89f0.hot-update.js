"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ui/jooka-hero-demo.tsx":
/*!*******************************************!*\
  !*** ./components/ui/jooka-hero-demo.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.mjs\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.mjs\");\n/* harmony import */ var _components_ui_minimalist_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/minimalist-hero */ \"(app-pages-browser)/./components/ui/minimalist-hero.tsx\");\n\n\n\n\nconst JookaHeroDemo = ()=>{\n    const navLinks = [\n        {\n            label: \"HOME\",\n            href: \"/\"\n        },\n        {\n            label: \"SHOP\",\n            href: \"/shop\"\n        },\n        {\n            label: \"ABOUT\",\n            href: \"/about\"\n        },\n        {\n            label: \"CART\",\n            href: \"/cart\"\n        }\n    ];\n    const socialLinks = [\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-black text-gold\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_minimalist_hero__WEBPACK_IMPORTED_MODULE_2__.MinimalistHero, {\n            logoText: \"JOOKA\",\n            navLinks: navLinks,\n            mainText: \"Discover luxury fashion that defines elegance and sophistication. Each piece is carefully curated to embody timeless style and exceptional quality.\",\n            readMoreLink: \"/about\",\n            imageSrc: \"/hero-img.png\",\n            imageAlt: \"Luxury fashion model showcasing JOOKA's elegant collection\",\n            overlayText: {\n                part1: \"less is\",\n                part2: \"more.\"\n            },\n            socialLinks: socialLinks,\n            locationText: \"New York, NY\",\n            className: \"bg-black text-gold font-serif\"\n        }, void 0, false, {\n            fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\jooka-hero-demo.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\cursor projects\\\\jooka-ecommerce\\\\components\\\\ui\\\\jooka-hero-demo.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = JookaHeroDemo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (JookaHeroDemo);\nvar _c;\n$RefreshReg$(_c, \"JookaHeroDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/jooka-hero-demo.tsx\n"));

/***/ })

});