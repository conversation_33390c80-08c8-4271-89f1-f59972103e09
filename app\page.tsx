'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import ProductCard from '@/components/ProductCard'
import JookaHeroDemo from '@/components/ui/jooka-hero-demo'

// Mock featured products data
const featuredProducts = [
  {
    id: '1',
    name: 'Silk Evening Dress',
    price: 299,
    image: 'https://picsum.photos/400/500?random=3',
    category: 'Dress<PERSON>'
  },
  {
    id: '2',
    name: 'Cashmere Blazer',
    price: 459,
    image: 'https://picsum.photos/400/500?random=4',
    category: 'Outerwear'
  },
  {
    id: '3',
    name: '<PERSON> Necklace',
    price: 189,
    image: 'https://picsum.photos/400/500?random=5',
    category: 'Accessories'
  },
  {
    id: '4',
    name: 'Leather Handbag',
    price: 329,
    image: 'https://picsum.photos/400/500?random=6',
    category: 'Bags'
  }
]

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <JookaHeroDemo />

      {/* Featured Products Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-serif font-bold text-gold mb-4">
              Featured Collection
            </h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              Discover our carefully curated selection of luxury pieces that embody timeless elegance and natural beauty.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>

          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Link href="/shop" className="btn-secondary">
              View All Products
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className="py-16 px-4 bg-charcoal/50">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl font-serif font-bold text-gold mb-6">
                Our Story
              </h2>
              <p className="text-gray-300 text-lg mb-6 leading-relaxed">
                JOOKA was born from a vision to create fashion that celebrates the natural elegance within every individual. Our designs blend timeless sophistication with contemporary sensibilities.
              </p>
              <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                Each piece is crafted with meticulous attention to detail, using only the finest materials sourced ethically from around the world.
              </p>
              <Link href="/about" className="btn-secondary">
                Learn More
              </Link>
            </motion.div>
            
            <motion.div
              className="relative h-96 lg:h-[500px]"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Image
                src="https://picsum.photos/600/500?random=2"
                alt="JOOKA Story"
                fill
                className="object-cover rounded-lg"
              />
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  )
}