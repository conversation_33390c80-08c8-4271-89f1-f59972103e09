'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ShoppingCart } from 'lucide-react'
import { useCartStore } from '@/store/cartStore'

interface Product {
  id: string
  name: string
  price: number
  image: string
  category?: string
}

interface ProductCardProps {
  product: Product
}

export default function ProductCard({ product }: ProductCardProps) {
  const addItem = useCartStore((state) => state.addItem)

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
    })
  }

  return (
    <motion.div
      className="card-product group"
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link href={`/product/${product.id}`}>
        <div className="relative aspect-[4/5] overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
          
          {/* Quick Add Button */}
          <button
            onClick={handleAddToCart}
            className="absolute bottom-4 right-4 bg-gold text-black p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110"
          >
            <ShoppingCart className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4">
          {product.category && (
            <p className="text-gold/70 text-sm uppercase tracking-wide mb-2">
              {product.category}
            </p>
          )}
          <h3 className="text-white font-medium mb-2 group-hover:text-gold transition-colors">
            {product.name}
          </h3>
          <p className="text-gold font-semibold text-lg">
            ${product.price}
          </p>
        </div>
      </Link>
    </motion.div>
  )
}