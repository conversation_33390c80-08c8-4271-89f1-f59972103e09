globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/about/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Footer.tsx":{"*":{"id":"(ssr)/./components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/Navbar.tsx":{"*":{"id":"(ssr)/./components/Navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/shop/page.tsx":{"*":{"id":"(ssr)/./app/shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/product/[id]/page.tsx":{"*":{"id":"(ssr)/./app/product/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\cursor projects\\jooka-ecommerce\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\components\\Footer.tsx":{"id":"(app-pages-browser)/./components/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\components\\Navbar.tsx":{"id":"(app-pages-browser)/./components/Navbar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\app\\shop\\page.tsx":{"id":"(app-pages-browser)/./app/shop/page.tsx","name":"*","chunks":[],"async":false},"D:\\cursor projects\\jooka-ecommerce\\app\\about\\page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"D:\\cursor projects\\jooka-ecommerce\\app\\product\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/product/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\cursor projects\\jooka-ecommerce\\":[],"D:\\cursor projects\\jooka-ecommerce\\app\\page":[],"D:\\cursor projects\\jooka-ecommerce\\app\\layout":["static/css/app/layout.css"],"D:\\cursor projects\\jooka-ecommerce\\app\\about\\page":[]}}