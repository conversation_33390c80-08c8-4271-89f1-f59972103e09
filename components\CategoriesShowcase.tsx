'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'

interface Category {
  id: string
  name: string
  description: string
  image: string
  href: string
  featured?: boolean
}

const categories: Category[] = [
  {
    id: '1',
    name: 'Dress<PERSON>',
    description: 'Elegant evening wear and sophisticated day dresses crafted from the finest materials',
    image: 'https://picsum.photos/600/800?random=10',
    href: '/shop?category=dresses',
    featured: true
  },
  {
    id: '2',
    name: 'Outerwear',
    description: 'Luxurious coats and blazers that define modern sophistication',
    image: 'https://picsum.photos/600/800?random=11',
    href: '/shop?category=outerwear'
  },
  {
    id: '3',
    name: 'Accessories',
    description: 'Timeless pieces that complete your elegant ensemble',
    image: 'https://picsum.photos/600/800?random=12',
    href: '/shop?category=accessories'
  },
  {
    id: '4',
    name: 'Bags',
    description: 'Handcrafted leather goods that embody luxury and functionality',
    image: 'https://picsum.photos/600/800?random=13',
    href: '/shop?category=bags'
  }
]

const CategoryCard = ({ category, index }: { category: Category; index: number }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 60 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: index * 0.2 }}
      viewport={{ once: true }}
      className={`group relative overflow-hidden ${
        category.featured ? 'md:col-span-2 md:row-span-2' : ''
      }`}
    >
      <Link href={category.href}>
        <div className="relative aspect-[4/5] md:aspect-auto md:h-full overflow-hidden bg-black/20">
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
          />
          
          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-500" />
          
          {/* Content */}
          <div className="absolute inset-0 flex flex-col justify-end p-8 md:p-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 + 0.3 }}
              viewport={{ once: true }}
              className="space-y-4"
            >
              {/* Category Badge */}
              <motion.div
                className="inline-block"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <span className="text-xs font-medium tracking-[0.2em] text-gold/80 uppercase bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full">
                  Collection
                </span>
              </motion.div>
              
              {/* Category Name */}
              <h3 className={`font-serif font-light text-ivory group-hover:text-gold transition-colors duration-300 ${
                category.featured ? 'text-4xl md:text-6xl' : 'text-2xl md:text-3xl'
              }`}>
                {category.name}
              </h3>
              
              {/* Description */}
              <p className={`text-ivory/80 font-light leading-relaxed group-hover:text-ivory transition-colors duration-300 ${
                category.featured ? 'text-lg md:text-xl max-w-2xl' : 'text-base max-w-xs'
              }`}>
                {category.description}
              </p>
              
              {/* CTA */}
              <motion.div
                className="flex items-center space-x-2 text-gold group-hover:text-ivory transition-colors duration-300"
                whileHover={{ x: 8 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-sm font-medium tracking-wide uppercase">
                  Explore
                </span>
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </motion.div>
          </div>
          
          {/* Hover Border Effect */}
          <motion.div
            className="absolute inset-0 border-2 border-gold/0 group-hover:border-gold/30 transition-colors duration-500"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
          />
        </div>
      </Link>
    </motion.div>
  )
}

export default function CategoriesShowcase() {
  return (
    <section className="relative py-24 px-8 md:px-12 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-black/98 to-black/95" />
      <div className="absolute top-1/4 right-0 w-96 h-96 bg-gold/5 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-gold/3 rounded-full blur-3xl" />
      
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.span
            className="text-sm font-medium tracking-[0.2em] text-gold/60 uppercase mb-4 block"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Discover
          </motion.span>
          
          <motion.h2
            className="text-5xl md:text-6xl lg:text-7xl font-serif font-light text-gold mb-8 tracking-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            Our
            <span className="block text-4xl md:text-5xl lg:text-6xl text-ivory/90 font-light italic">
              Collections
            </span>
          </motion.h2>
          
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            whileInView={{ opacity: 1, width: "4rem" }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="h-px bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"
          />
          
          <motion.p
            className="text-lg md:text-xl text-ivory/70 max-w-3xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            Each collection tells a story of 
            <span className="text-gold/80 font-medium"> craftsmanship</span>, 
            <span className="text-gold/80 font-medium"> elegance</span>, and 
            <span className="text-gold/80 font-medium"> timeless design</span>.
          </motion.p>
        </motion.div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 auto-rows-fr">
          {categories.map((category, index) => (
            <CategoryCard key={category.id} category={category} index={index} />
          ))}
        </div>
      </div>
    </section>
  )
}
